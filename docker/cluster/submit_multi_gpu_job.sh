#!/usr/bin/env bash

# Multi-GPU Training Job Submission Script for SLURM
# This script provides easy configuration for multi-GPU training jobs

set -e  # Exit on any error

# Default configuration
DEFAULT_NUM_GPUS=2
DEFAULT_GPU_TYPE="a100_80gb"
DEFAULT_CPUS_PER_GPU=16
DEFAULT_MEM_PER_CPU=16384
DEFAULT_TIME="23:00:00"
DEFAULT_TASK="Isaac-m545-digging"
DEFAULT_NUM_ENVS=64000
DEFAULT_MAX_ITERATIONS=1000

# Function to display usage
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Multi-GPU Training Job Submission Script

OPTIONS:
    -g, --gpus NUM_GPUS         Number of GPUs to use (default: $DEFAULT_NUM_GPUS)
    -t, --gpu-type GPU_TYPE     GPU type to request (default: $DEFAULT_GPU_TYPE)
                                Available types: a100_80gb, rtx_3090, rtx_4090, v100, etc.
    -c, --cpus-per-gpu CPUS     CPUs per GPU (default: $DEFAULT_CPUS_PER_GPU)
    -m, --mem-per-cpu MEM       Memory per CPU in MB (default: $DEFAULT_MEM_PER_CPU)
    -T, --time TIME             Job time limit (default: $DEFAULT_TIME)
    --task TASK                 Training task name (default: $DEFAULT_TASK)
    --num-envs NUM_ENVS         Number of environments (default: $DEFAULT_NUM_ENVS)
    --max-iterations ITERS      Maximum training iterations (default: $DEFAULT_MAX_ITERATIONS)
    --use-multi-gpu-script      Use the multi-GPU training script instead of regular script
    -h, --help                  Show this help message

EXAMPLES:
    # Train with 2 A100 GPUs
    $0 --gpus 2 --gpu-type a100_80gb

    # Train with 4 RTX 3090 GPUs with custom task
    $0 --gpus 4 --gpu-type rtx_3090 --task Isaac-m545-single-boulder

    # Train with 8 GPUs and more environments
    $0 --gpus 8 --num-envs 128000 --max-iterations 2000

    # Single GPU training (equivalent to original script)
    $0 --gpus 1

NOTES:
    - Total environments will be distributed across all GPUs
    - Each GPU will handle NUM_ENVS/NUM_GPUS environments
    - Logging and video recording only happen on GPU 0 to avoid conflicts
    - For optimal performance, ensure NUM_ENVS is divisible by NUM_GPUS
EOF
}

# Parse command line arguments
NUM_GPUS=$DEFAULT_NUM_GPUS
GPU_TYPE=$DEFAULT_GPU_TYPE
CPUS_PER_GPU=$DEFAULT_CPUS_PER_GPU
MEM_PER_CPU=$DEFAULT_MEM_PER_CPU
TIME=$DEFAULT_TIME
TASK=$DEFAULT_TASK
NUM_ENVS=$DEFAULT_NUM_ENVS
MAX_ITERATIONS=$DEFAULT_MAX_ITERATIONS
USE_MULTI_GPU_SCRIPT=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -g|--gpus)
            NUM_GPUS="$2"
            shift 2
            ;;
        -t|--gpu-type)
            GPU_TYPE="$2"
            shift 2
            ;;
        -c|--cpus-per-gpu)
            CPUS_PER_GPU="$2"
            shift 2
            ;;
        -m|--mem-per-cpu)
            MEM_PER_CPU="$2"
            shift 2
            ;;
        -T|--time)
            TIME="$2"
            shift 2
            ;;
        --task)
            TASK="$2"
            shift 2
            ;;
        --num-envs)
            NUM_ENVS="$2"
            shift 2
            ;;
        --max-iterations)
            MAX_ITERATIONS="$2"
            shift 2
            ;;
        --use-multi-gpu-script)
            USE_MULTI_GPU_SCRIPT=true
            shift
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate inputs
if ! [[ "$NUM_GPUS" =~ ^[0-9]+$ ]] || [ "$NUM_GPUS" -lt 1 ]; then
    echo "Error: NUM_GPUS must be a positive integer"
    exit 1
fi

if ! [[ "$NUM_ENVS" =~ ^[0-9]+$ ]] || [ "$NUM_ENVS" -lt 1 ]; then
    echo "Error: NUM_ENVS must be a positive integer"
    exit 1
fi

# Check if NUM_ENVS is divisible by NUM_GPUS for optimal distribution
if [ $((NUM_ENVS % NUM_GPUS)) -ne 0 ]; then
    echo "Warning: NUM_ENVS ($NUM_ENVS) is not divisible by NUM_GPUS ($NUM_GPUS)"
    echo "This may lead to uneven environment distribution across GPUs"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Calculate resources
TOTAL_CPUS=$((NUM_GPUS * CPUS_PER_GPU))
TOTAL_MEM=$((NUM_GPUS * CPUS_PER_GPU * MEM_PER_CPU / 1024))  # Convert to GB
ENVS_PER_GPU=$((NUM_ENVS / NUM_GPUS))

# Display configuration
echo "=========================================="
echo "Multi-GPU Training Configuration"
echo "=========================================="
echo "Number of GPUs: $NUM_GPUS"
echo "GPU Type: $GPU_TYPE"
echo "CPUs per GPU: $CPUS_PER_GPU"
echo "Total CPUs: $TOTAL_CPUS"
echo "Total Memory: ${TOTAL_MEM}GB"
echo "Task: $TASK"
echo "Total Environments: $NUM_ENVS"
echo "Environments per GPU: $ENVS_PER_GPU"
echo "Max Iterations: $MAX_ITERATIONS"
echo "Job Time Limit: $TIME"
echo "Use Multi-GPU Script: $USE_MULTI_GPU_SCRIPT"
echo "=========================================="

# Determine which training script to use
if [ "$USE_MULTI_GPU_SCRIPT" = true ] || [ "$NUM_GPUS" -gt 1 ]; then
    PYTHON_SCRIPT="scripts/rl/rsl_rl/train_multi_gpu.py"
    echo "Using multi-GPU training script: $PYTHON_SCRIPT"
else
    PYTHON_SCRIPT="scripts/rl/rsl_rl/train.py"
    echo "Using single-GPU training script: $PYTHON_SCRIPT"
fi

# Export environment variables for the submit script
export NUM_GPUS
export GPU_TYPE
export CPUS_PER_GPU
export MEM_PER_CPU

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"

# Update the cluster configuration to use the appropriate script
export CLUSTER_PYTHON_EXECUTABLE="$PYTHON_SCRIPT"

# Submit the job
echo "Submitting job..."
bash "$SCRIPT_DIR/submit_job_slurm.sh" "$SCRIPT_DIR/../.." moleworks_ext \
    --headless \
    --task "$TASK" \
    --num_envs "$NUM_ENVS" \
    --max_iterations "$MAX_ITERATIONS"

echo "Job submitted successfully!"
echo "Monitor job status with: squeue -u \$USER"
echo "View job output with: tail -f slurm-<job_id>.out"
